//+------------------------------------------------------------------+
//|                                       Setup_MicroScalperFX.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property script_show_inputs

//--- Input parameters
input bool     RunFullSetup = true;           // Run complete setup validation
input bool     TestIndicators = true;         // Test all indicators
input bool     TestTrading = true;            // Test trading functions
input bool     ShowRecommendations = true;    // Show optimization recommendations

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== MicroScalperFX EA Setup & Validation ===");
    Print("Version: 1.00");
    Print("Date: ", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
    Print("Account: ", AccountInfoString(ACCOUNT_NAME), " (", AccountInfoInteger(ACCOUNT_LOGIN), ")");
    Print("Balance: $", AccountInfoDouble(ACCOUNT_BALANCE));
    Print("===============================================");
    
    if(RunFullSetup)
    {
        //--- Step 1: Environment Check
        CheckEnvironment();
        
        //--- Step 2: Indicator Validation
        if(TestIndicators)
            ValidateIndicators();
        
        //--- Step 3: Trading Setup
        if(TestTrading)
            ValidateTradingSetup();
        
        //--- Step 4: Risk Management
        ValidateRiskManagement();
        
        //--- Step 5: Broker Compatibility
        CheckBrokerCompatibility();
        
        //--- Step 6: Recommendations
        if(ShowRecommendations)
            ShowOptimizationRecommendations();
    }
    
    Print("===============================================");
    Print("=== Setup Validation Complete ===");
}

//+------------------------------------------------------------------+
//| Check MT5 environment and requirements                          |
//+------------------------------------------------------------------+
void CheckEnvironment()
{
    Print("--- Environment Check ---");
    
    //--- Check MT5 version
    Print("MT5 Build: ", TerminalInfoInteger(TERMINAL_BUILD));
    Print("MT5 Path: ", TerminalInfoString(TERMINAL_PATH));
    
    //--- Check trading permissions
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
    {
        Print("⚠️  WARNING: Automated trading is disabled in terminal");
        Print("   Solution: Enable 'Allow automated trading' in Tools->Options->Expert Advisors");
    }
    else
    {
        Print("✓ Automated trading is enabled");
    }
    
    //--- Check DLL imports
    if(!TerminalInfoInteger(TERMINAL_DLLS_ALLOWED))
    {
        Print("ℹ️  INFO: DLL imports disabled (not required for this EA)");
    }
    
    //--- Check connection
    if(!TerminalInfoInteger(TERMINAL_CONNECTED))
    {
        Print("⚠️  WARNING: Terminal not connected to trade server");
    }
    else
    {
        Print("✓ Terminal connected to trade server");
    }
}

//+------------------------------------------------------------------+
//| Validate all required indicators                                |
//+------------------------------------------------------------------+
void ValidateIndicators()
{
    Print("--- Indicator Validation ---");
    
    string testSymbol = _Symbol;
    ENUM_TIMEFRAMES testTF = PERIOD_M5;
    
    //--- Test Supertrend
    int supertrendHandle = iCustom(testSymbol, testTF, "Supertrend", 10, 3.0);
    if(supertrendHandle != INVALID_HANDLE)
    {
        double buffer[];
        if(CopyBuffer(supertrendHandle, 0, 0, 1, buffer) > 0)
        {
            Print("✓ Supertrend indicator working correctly");
        }
        else
        {
            Print("⚠️  WARNING: Supertrend indicator not returning data");
        }
        IndicatorRelease(supertrendHandle);
    }
    else
    {
        Print("❌ ERROR: Supertrend indicator not found or failed to load");
        Print("   Solution: Ensure Supertrend.mq5 is compiled and in Indicators folder");
    }
    
    //--- Test MACD
    int macdHandle = iMACD(testSymbol, testTF, 12, 26, 9, PRICE_CLOSE);
    if(macdHandle != INVALID_HANDLE)
    {
        Print("✓ MACD indicator available");
        IndicatorRelease(macdHandle);
    }
    else
    {
        Print("❌ ERROR: MACD indicator failed to load");
    }
    
    //--- Test EMA
    int emaHandle = iMA(testSymbol, testTF, 200, 0, MODE_EMA, PRICE_CLOSE);
    if(emaHandle != INVALID_HANDLE)
    {
        Print("✓ EMA indicator available");
        IndicatorRelease(emaHandle);
    }
    else
    {
        Print("❌ ERROR: EMA indicator failed to load");
    }
    
    //--- Test ATR
    int atrHandle = iATR(testSymbol, testTF, 14);
    if(atrHandle != INVALID_HANDLE)
    {
        Print("✓ ATR indicator available");
        IndicatorRelease(atrHandle);
    }
    else
    {
        Print("❌ ERROR: ATR indicator failed to load");
    }
}

//+------------------------------------------------------------------+
//| Validate trading setup and permissions                          |
//+------------------------------------------------------------------+
void ValidateTradingSetup()
{
    Print("--- Trading Setup Validation ---");
    
    string symbol = _Symbol;
    
    //--- Check symbol trading permissions
    if(SymbolInfoInteger(symbol, SYMBOL_TRADE_MODE) == SYMBOL_TRADE_MODE_DISABLED)
    {
        Print("❌ ERROR: Trading disabled for ", symbol);
        return;
    }
    else
    {
        Print("✓ Trading enabled for ", symbol);
    }
    
    //--- Check account trading permissions
    if(!AccountInfoInteger(ACCOUNT_TRADE_ALLOWED))
    {
        Print("❌ ERROR: Trading disabled for this account");
        return;
    }
    else
    {
        Print("✓ Account trading permissions OK");
    }
    
    //--- Check minimum lot size
    double minLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
    
    Print("Lot sizes - Min: ", minLot, " Max: ", maxLot, " Step: ", lotStep);
    
    if(minLot <= 0.01)
    {
        Print("✓ Minimum lot size suitable for small accounts");
    }
    else
    {
        Print("⚠️  WARNING: Minimum lot size (", minLot, ") may be too large for $50 account");
    }
    
    //--- Check spread
    double bid = SymbolInfoDouble(symbol, SYMBOL_BID);
    double ask = SymbolInfoDouble(symbol, SYMBOL_ASK);
    double spread = (ask - bid) / SymbolInfoDouble(symbol, SYMBOL_POINT);
    
    Print("Current spread: ", spread, " points");
    
    if(spread <= 15) // 1.5 pips
    {
        Print("✓ Spread within acceptable range");
    }
    else
    {
        Print("⚠️  WARNING: Current spread (", spread, ") exceeds recommended maximum (15 points)");
    }
}

//+------------------------------------------------------------------+
//| Validate risk management settings                               |
//+------------------------------------------------------------------+
void ValidateRiskManagement()
{
    Print("--- Risk Management Validation ---");
    
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = 0.50; // Default risk per trade
    double riskPercent = (riskAmount / balance) * 100;
    
    Print("Account balance: $", balance);
    Print("Risk per trade: $", riskAmount, " (", DoubleToString(riskPercent, 2), "%)");
    
    if(riskPercent <= 2.0)
    {
        Print("✓ Risk per trade within conservative limits");
    }
    else
    {
        Print("⚠️  WARNING: Risk per trade (", DoubleToString(riskPercent, 2), "%) exceeds 2%");
        Print("   Recommendation: Reduce risk amount to $", DoubleToString(balance * 0.02, 2));
    }
    
    //--- Check margin requirements
    double marginRequired = 0;
    if(OrderCalcMargin(ORDER_TYPE_BUY, _Symbol, 0.01, SymbolInfoDouble(_Symbol, SYMBOL_ASK), marginRequired))
    {
        Print("Margin required for 0.01 lot: $", marginRequired);
        
        double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
        if(freeMargin > marginRequired * 10)
        {
            Print("✓ Sufficient margin available");
        }
        else
        {
            Print("⚠️  WARNING: Low free margin. Consider reducing position size.");
        }
    }
}

//+------------------------------------------------------------------+
//| Check broker compatibility                                       |
//+------------------------------------------------------------------+
void CheckBrokerCompatibility()
{
    Print("--- Broker Compatibility Check ---");
    
    string company = AccountInfoString(ACCOUNT_COMPANY);
    string server = AccountInfoString(ACCOUNT_SERVER);
    
    Print("Broker: ", company);
    Print("Server: ", server);
    
    //--- Check execution mode
    ENUM_SYMBOL_TRADE_EXECUTION execution = (ENUM_SYMBOL_TRADE_EXECUTION)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_EXEMODE);
    
    switch(execution)
    {
        case SYMBOL_TRADE_EXECUTION_REQUEST:
            Print("✓ Execution: Request execution (good for scalping)");
            break;
        case SYMBOL_TRADE_EXECUTION_INSTANT:
            Print("✓ Execution: Instant execution (good for scalping)");
            break;
        case SYMBOL_TRADE_EXECUTION_MARKET:
            Print("✓ Execution: Market execution (acceptable for scalping)");
            break;
        case SYMBOL_TRADE_EXECUTION_EXCHANGE:
            Print("ℹ️  Execution: Exchange execution");
            break;
    }
    
    //--- Check filling modes
    int filling = (int)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);
    
    if(filling & SYMBOL_FILLING_FOK)
        Print("✓ Fill or Kill orders supported");
    if(filling & SYMBOL_FILLING_IOC)
        Print("✓ Immediate or Cancel orders supported");
    
    //--- Check stop level
    int stopLevel = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
    Print("Stop level: ", stopLevel, " points");
    
    if(stopLevel <= 10)
    {
        Print("✓ Stop level suitable for scalping");
    }
    else
    {
        Print("⚠️  WARNING: High stop level (", stopLevel, ") may affect scalping performance");
    }
}

//+------------------------------------------------------------------+
//| Show optimization recommendations                                |
//+------------------------------------------------------------------+
void ShowOptimizationRecommendations()
{
    Print("--- Optimization Recommendations ---");
    
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    
    if(balance < 100)
    {
        Print("💡 For accounts under $100:");
        Print("   - Use M5 timeframe for more stable signals");
        Print("   - Limit to 3 trades per day");
        Print("   - Risk maximum $0.50 per trade");
        Print("   - Focus on EURUSD during London session");
    }
    else if(balance < 500)
    {
        Print("💡 For accounts $100-$500:");
        Print("   - Can use M1 or M5 timeframe");
        Print("   - Up to 5 trades per day");
        Print("   - Risk 1% per trade");
        Print("   - Trade major pairs during high liquidity");
    }
    else
    {
        Print("💡 For accounts over $500:");
        Print("   - Use M1 timeframe for maximum frequency");
        Print("   - Up to 10 trades per day");
        Print("   - Risk 1-2% per trade");
        Print("   - Consider multiple pairs simultaneously");
    }
    
    Print("");
    Print("🔧 General Optimization Tips:");
    Print("   - Use VPS for consistent execution");
    Print("   - Monitor during London/NY overlap (12:00-16:00 GMT)");
    Print("   - Avoid trading during major news releases");
    Print("   - Backtest with 99.9% quality real tick data");
    Print("   - Start with demo account for 1-2 weeks");
}
