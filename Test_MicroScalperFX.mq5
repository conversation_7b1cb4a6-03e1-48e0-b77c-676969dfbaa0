//+------------------------------------------------------------------+
//|                                        Test_MicroScalperFX.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property script_show_inputs

//--- Include necessary libraries
#include <Trade\Trade.mqh>

//--- Input parameters
input string   TestSymbol = "EURUSD";          // Symbol to test
input ENUM_TIMEFRAMES TestTimeframe = PERIOD_M5; // Timeframe for testing
input int      TestBars = 1000;                // Number of bars to test

//--- Global variables
CTrade trade;

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== MicroScalperFX EA Test Started ===");
    
    //--- Test 1: Indicator Availability
    TestIndicatorAvailability();
    
    //--- Test 2: Symbol Information
    TestSymbolInformation();
    
    //--- Test 3: Trading Functions
    TestTradingFunctions();
    
    //--- Test 4: Risk Management
    TestRiskManagement();
    
    //--- Test 5: Signal Detection
    TestSignalDetection();
    
    Print("=== MicroScalperFX EA Test Completed ===");
}

//+------------------------------------------------------------------+
//| Test indicator availability and functionality                   |
//+------------------------------------------------------------------+
void TestIndicatorAvailability()
{
    Print("--- Testing Indicator Availability ---");
    
    //--- Test Supertrend
    int supertrendHandle = iCustom(TestSymbol, TestTimeframe, "Supertrend", 10, 3.0);
    if(supertrendHandle != INVALID_HANDLE)
    {
        Print("✓ Supertrend indicator loaded successfully");
        IndicatorRelease(supertrendHandle);
    }
    else
    {
        Print("✗ Failed to load Supertrend indicator");
    }
    
    //--- Test MACD
    int macdHandle = iMACD(TestSymbol, TestTimeframe, 12, 26, 9, PRICE_CLOSE);
    if(macdHandle != INVALID_HANDLE)
    {
        Print("✓ MACD indicator loaded successfully");
        IndicatorRelease(macdHandle);
    }
    else
    {
        Print("✗ Failed to load MACD indicator");
    }
    
    //--- Test EMA
    int emaHandle = iMA(TestSymbol, TestTimeframe, 200, 0, MODE_EMA, PRICE_CLOSE);
    if(emaHandle != INVALID_HANDLE)
    {
        Print("✓ EMA indicator loaded successfully");
        IndicatorRelease(emaHandle);
    }
    else
    {
        Print("✗ Failed to load EMA indicator");
    }
    
    //--- Test ATR
    int atrHandle = iATR(TestSymbol, TestTimeframe, 14);
    if(atrHandle != INVALID_HANDLE)
    {
        Print("✓ ATR indicator loaded successfully");
        IndicatorRelease(atrHandle);
    }
    else
    {
        Print("✗ Failed to load ATR indicator");
    }
}

//+------------------------------------------------------------------+
//| Test symbol information and trading conditions                  |
//+------------------------------------------------------------------+
void TestSymbolInformation()
{
    Print("--- Testing Symbol Information ---");
    
    //--- Check if symbol exists
    if(!SymbolSelect(TestSymbol, true))
    {
        Print("✗ Symbol ", TestSymbol, " not available");
        return;
    }
    
    Print("✓ Symbol ", TestSymbol, " is available");
    
    //--- Get symbol properties
    double bid = SymbolInfoDouble(TestSymbol, SYMBOL_BID);
    double ask = SymbolInfoDouble(TestSymbol, SYMBOL_ASK);
    double spread = (ask - bid) / SymbolInfoDouble(TestSymbol, SYMBOL_POINT);
    double minLot = SymbolInfoDouble(TestSymbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(TestSymbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(TestSymbol, SYMBOL_VOLUME_STEP);
    
    Print("Bid: ", bid, " Ask: ", ask, " Spread: ", spread, " points");
    Print("Min Lot: ", minLot, " Max Lot: ", maxLot, " Lot Step: ", lotStep);
    
    //--- Check trading permissions
    if(SymbolInfoInteger(TestSymbol, SYMBOL_TRADE_MODE) == SYMBOL_TRADE_MODE_DISABLED)
    {
        Print("✗ Trading is disabled for ", TestSymbol);
    }
    else
    {
        Print("✓ Trading is enabled for ", TestSymbol);
    }
}

//+------------------------------------------------------------------+
//| Test trading functions                                           |
//+------------------------------------------------------------------+
void TestTradingFunctions()
{
    Print("--- Testing Trading Functions ---");
    
    //--- Test lot size calculation
    double testSLDistance = 0.0010; // 10 pips for EURUSD
    double riskAmount = 0.50;
    
    double tickValue = SymbolInfoDouble(TestSymbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(TestSymbol, SYMBOL_TRADE_TICK_SIZE);
    double minLot = SymbolInfoDouble(TestSymbol, SYMBOL_VOLUME_MIN);
    double lotStep = SymbolInfoDouble(TestSymbol, SYMBOL_VOLUME_STEP);
    
    if(tickValue > 0 && tickSize > 0)
    {
        double lotSize = (riskAmount * tickSize) / (testSLDistance * tickValue);
        lotSize = MathFloor(lotSize / lotStep) * lotStep;
        
        if(lotSize < minLot) lotSize = minLot;
        
        Print("✓ Lot size calculation: ", lotSize, " lots for $", riskAmount, " risk");
    }
    else
    {
        Print("✗ Invalid tick value or tick size");
    }
    
    //--- Test spread checking
    double currentSpread = (SymbolInfoDouble(TestSymbol, SYMBOL_ASK) - SymbolInfoDouble(TestSymbol, SYMBOL_BID)) / SymbolInfoDouble(TestSymbol, SYMBOL_POINT);
    double maxSpread = 15; // 1.5 pips * 10
    
    if(currentSpread <= maxSpread)
    {
        Print("✓ Spread check passed: ", currentSpread, " <= ", maxSpread);
    }
    else
    {
        Print("✗ Spread too high: ", currentSpread, " > ", maxSpread);
    }
}

//+------------------------------------------------------------------+
//| Test risk management functions                                  |
//+------------------------------------------------------------------+
void TestRiskManagement()
{
    Print("--- Testing Risk Management ---");
    
    //--- Test daily drawdown calculation
    double startBalance = 1000.0;
    double currentBalance = 970.0;
    double drawdown = (startBalance - currentBalance) / startBalance * 100;
    double maxDrawdown = 3.0;
    
    Print("Drawdown calculation: ", drawdown, "%");
    
    if(drawdown <= maxDrawdown)
    {
        Print("✓ Drawdown within limits: ", drawdown, "% <= ", maxDrawdown, "%");
    }
    else
    {
        Print("✗ Drawdown exceeded: ", drawdown, "% > ", maxDrawdown, "%");
    }
    
    //--- Test time filtering
    datetime currentTime = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(currentTime, dt);
    
    int startHour = 9;
    int endHour = 18;
    
    if(dt.hour >= startHour && dt.hour < endHour)
    {
        Print("✓ Current time within trading hours: ", dt.hour, ":00");
    }
    else
    {
        Print("✗ Current time outside trading hours: ", dt.hour, ":00");
    }
}

//+------------------------------------------------------------------+
//| Test signal detection logic                                     |
//+------------------------------------------------------------------+
void TestSignalDetection()
{
    Print("--- Testing Signal Detection ---");
    
    //--- Load indicators for testing
    int supertrendHandle = iCustom(TestSymbol, TestTimeframe, "Supertrend", 10, 3.0);
    int macdHandle = iMACD(TestSymbol, TestTimeframe, 12, 26, 9, PRICE_CLOSE);
    int emaHandle = iMA(TestSymbol, TestTimeframe, 200, 0, MODE_EMA, PRICE_CLOSE);
    
    if(supertrendHandle == INVALID_HANDLE || macdHandle == INVALID_HANDLE || emaHandle == INVALID_HANDLE)
    {
        Print("✗ Failed to load indicators for signal testing");
        return;
    }
    
    //--- Get indicator values
    double supertrendColor[];
    double macdMain[];
    double emaValues[];
    
    ArraySetAsSeries(supertrendColor, true);
    ArraySetAsSeries(macdMain, true);
    ArraySetAsSeries(emaValues, true);
    
    if(CopyBuffer(supertrendHandle, 1, 0, 3, supertrendColor) > 0 &&
       CopyBuffer(macdHandle, 0, 0, 3, macdMain) > 0 &&
       CopyBuffer(emaHandle, 0, 0, 3, emaValues) > 0)
    {
        Print("✓ Indicator values retrieved successfully");
        
        //--- Test signal conditions
        double close = iClose(TestSymbol, TestTimeframe, 1);
        
        //--- Bullish signal test
        bool bullishTrend = close > emaValues[1];
        bool bullishSupertrend = supertrendColor[1] == 0;
        bool bullishMACD = macdMain[1] > 0 && macdMain[1] > macdMain[2];
        
        Print("Bullish conditions - Trend: ", bullishTrend, " Supertrend: ", bullishSupertrend, " MACD: ", bullishMACD);
        
        //--- Bearish signal test
        bool bearishTrend = close < emaValues[1];
        bool bearishSupertrend = supertrendColor[1] == 1;
        bool bearishMACD = macdMain[1] < 0 && macdMain[1] < macdMain[2];
        
        Print("Bearish conditions - Trend: ", bearishTrend, " Supertrend: ", bearishSupertrend, " MACD: ", bearishMACD);
    }
    else
    {
        Print("✗ Failed to retrieve indicator values");
    }
    
    //--- Release handles
    IndicatorRelease(supertrendHandle);
    IndicatorRelease(macdHandle);
    IndicatorRelease(emaHandle);
}
