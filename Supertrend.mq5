//+------------------------------------------------------------------+
//|                                                   Supertrend.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Supertrend Indicator for MT5"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   1

//--- Plot settings
#property indicator_label1  "Supertrend"
#property indicator_type1   DRAW_COLOR_LINE
#property indicator_color1  clrLime,clrRed
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

//--- Input parameters
input int      InpPeriod = 10;        // ATR Period
input double   InpMultiplier = 3.0;   // ATR Multiplier

//--- Indicator buffers
double SupertrendBuffer[];
double ColorBuffer[];

//--- Global variables
int atrHandle;
double atrBuffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set indicator buffers
    SetIndexBuffer(0, SupertrendBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, ColorBuffer, INDICATOR_COLOR_INDEX);
    
    //--- Set buffer properties
    ArraySetAsSeries(SupertrendBuffer, true);
    ArraySetAsSeries(ColorBuffer, true);
    
    //--- Create ATR handle
    atrHandle = iATR(_Symbol, PERIOD_CURRENT, InpPeriod);
    if(atrHandle == INVALID_HANDLE)
    {
        Print("Error creating ATR indicator");
        return INIT_FAILED;
    }
    
    ArraySetAsSeries(atrBuffer, true);
    
    //--- Set indicator properties
    IndicatorSetString(INDICATOR_SHORTNAME, "Supertrend(" + IntegerToString(InpPeriod) + "," + DoubleToString(InpMultiplier, 1) + ")");
    IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(atrHandle != INVALID_HANDLE)
        IndicatorRelease(atrHandle);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    //--- Check for minimum bars
    if(rates_total < InpPeriod + 1)
        return 0;
    
    //--- Set arrays as series
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    //--- Get ATR values
    int copied = CopyBuffer(atrHandle, 0, 0, rates_total, atrBuffer);
    if(copied <= 0)
        return 0;
    
    //--- Calculate start position
    int start = prev_calculated;
    if(start == 0)
        start = InpPeriod;
    
    //--- Main calculation loop
    for(int i = start; i < rates_total; i++)
    {
        int pos = rates_total - 1 - i;
        
        //--- Calculate basic upper and lower bands
        double hl2 = (high[pos] + low[pos]) / 2.0;
        double atr = atrBuffer[pos];
        
        double basicUpperBand = hl2 + (InpMultiplier * atr);
        double basicLowerBand = hl2 - (InpMultiplier * atr);
        
        //--- Calculate final upper and lower bands
        double finalUpperBand, finalLowerBand;
        
        if(pos == rates_total - 1)
        {
            finalUpperBand = basicUpperBand;
            finalLowerBand = basicLowerBand;
        }
        else
        {
            int prevPos = pos + 1;
            double prevFinalUpperBand = SupertrendBuffer[prevPos];
            double prevFinalLowerBand = SupertrendBuffer[prevPos];
            
            //--- Calculate final upper band
            if(basicUpperBand < prevFinalUpperBand || close[prevPos] > prevFinalUpperBand)
                finalUpperBand = basicUpperBand;
            else
                finalUpperBand = prevFinalUpperBand;
            
            //--- Calculate final lower band
            if(basicLowerBand > prevFinalLowerBand || close[prevPos] < prevFinalLowerBand)
                finalLowerBand = basicLowerBand;
            else
                finalLowerBand = prevFinalLowerBand;
        }
        
        //--- Determine Supertrend direction and value
        if(pos == rates_total - 1)
        {
            if(close[pos] <= finalLowerBand)
            {
                SupertrendBuffer[pos] = finalUpperBand;
                ColorBuffer[pos] = 1; // Red/Bearish
            }
            else
            {
                SupertrendBuffer[pos] = finalLowerBand;
                ColorBuffer[pos] = 0; // Green/Bullish
            }
        }
        else
        {
            int prevPos = pos + 1;
            double prevSupertrend = SupertrendBuffer[prevPos];
            double prevColor = ColorBuffer[prevPos];
            
            if(prevColor == 1 && close[pos] > finalUpperBand)
            {
                SupertrendBuffer[pos] = finalLowerBand;
                ColorBuffer[pos] = 0; // Green/Bullish
            }
            else if(prevColor == 0 && close[pos] < finalLowerBand)
            {
                SupertrendBuffer[pos] = finalUpperBand;
                ColorBuffer[pos] = 1; // Red/Bearish
            }
            else
            {
                if(prevColor == 0)
                {
                    SupertrendBuffer[pos] = finalLowerBand;
                    ColorBuffer[pos] = 0; // Green/Bullish
                }
                else
                {
                    SupertrendBuffer[pos] = finalUpperBand;
                    ColorBuffer[pos] = 1; // Red/Bearish
                }
            }
        }
    }
    
    return rates_total;
}
