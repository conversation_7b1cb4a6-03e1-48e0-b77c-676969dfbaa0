# MicroScalperFX Expert Advisor

## 🎯 Overview
MicroScalperFX is a professional-grade scalping Expert Advisor designed specifically for small forex accounts ($50+). It implements a sophisticated multi-indicator confirmation system with ultra-conservative risk management, making it ideal for prop trading and capital preservation strategies.

## ✨ Key Features

### 🔄 Trading Strategy
- **Multi-Indicator Confirmation**: Supertrend + MACD + EMA + ATR
- **High-Frequency Scalping**: Optimized for M1/M5 timeframes
- **Pattern Recognition**: Bullish/bearish pin bars and engulfing patterns
- **Session Filtering**: London/NY sessions (9:00-18:00 GMT)
- **Spread Control**: Maximum 1.5 pips spread filter

### 🛡️ Risk Management
- **Fixed Risk**: $0.50 per trade (adjustable)
- **Dynamic Position Sizing**: ATR-based lot calculation
- **Daily Limits**: Maximum 5 trades per day per symbol
- **Drawdown Protection**: 3% daily maximum drawdown
- **Consecutive Loss Protection**: 2-hour lockout after 2 losses
- **Break-Even**: Automatic SL adjustment after first TP

### 📊 Performance Targets
- **Win Rate**: 60-75%
- **Risk/Reward**: 1.5:1 (adjustable)
- **Monthly Return**: 10-30%
- **Maximum Drawdown**: ≤10%
- **Trade Frequency**: 15-30 trades per week

## 📁 File Structure

```
MicroScalperFX/
├── MicroScalperFX_EA.mq5          # Main Expert Advisor
├── Supertrend.mq5                 # Custom Supertrend indicator
├── Test_MicroScalperFX.mq5        # Testing and validation script
├── Setup_MicroScalperFX.mq5       # Setup and optimization script
├── MicroScalperFX_Documentation.md # Detailed documentation
└── README.md                       # This file
```

## 🚀 Quick Start Guide

### Step 1: Installation
1. Copy all `.mq5` files to your MT5 data folder:
   - `MicroScalperFX_EA.mq5` → `MQL5/Experts/`
   - `Supertrend.mq5` → `MQL5/Indicators/`
   - `Test_MicroScalperFX.mq5` → `MQL5/Scripts/`
   - `Setup_MicroScalperFX.mq5` → `MQL5/Scripts/`

### Step 2: Compilation
1. Open MetaEditor (F4 in MT5)
2. Compile in this order:
   - `Supertrend.mq5` (indicator first)
   - `MicroScalperFX_EA.mq5`
   - Test scripts

### Step 3: Validation
1. Run `Setup_MicroScalperFX` script on your chart
2. Check all validation results
3. Run `Test_MicroScalperFX` script for functionality tests
4. Verify all indicators load correctly

### Step 4: Demo Testing
1. Attach EA to EURUSD M5 chart
2. Use default settings for $50 account
3. Run for 1-2 weeks on demo
4. Monitor performance and adjust if needed

### Step 5: Live Trading
1. Start with minimum risk settings
2. Monitor first few trades closely
3. Gradually optimize based on performance
4. Use VPS for consistent execution

## ⚙️ Configuration

### Recommended Settings for $50 Account
```
Risk Amount: $0.50
Max Spread: 1.5 pips
Max Trades/Day: 3-5
Risk/Reward: 1.5
Timeframe: M5
Pairs: EURUSD, USDJPY, GBPUSD
```

### Recommended Settings for $100+ Account
```
Risk Amount: 1% of balance
Max Spread: 2.0 pips
Max Trades/Day: 5-7
Risk/Reward: 1.5-2.0
Timeframe: M1 or M5
Pairs: All major pairs
```

## 🏆 Broker Requirements

### Essential Features
- ✅ ECN/STP execution
- ✅ Spreads ≤ 1.5 pips on majors
- ✅ Minimum lot 0.01
- ✅ Leverage 1:100+
- ✅ Fast execution (<100ms)

### Recommended Brokers
- **IC Markets** - Excellent for scalping
- **Pepperstone** - Low spreads, fast execution
- **FTMO** - Prop trading compatible
- **MyForexFunds** - Prop trading compatible

## 📈 Strategy Logic

### Entry Signals
The EA uses a 4-filter confirmation system:

1. **Trend Filter**: Price vs 200 EMA
2. **Direction Filter**: Supertrend color
3. **Momentum Filter**: MACD histogram
4. **Pattern Filter**: Pin bar/engulfing patterns

### Exit Strategy
- **Take Profit**: 1.5x stop loss distance
- **Stop Loss**: ATR(14) × 0.7
- **Break-Even**: After 1:1 profit reached
- **Time Exit**: End of trading session

## 🔧 Troubleshooting

### Common Issues

**No Trades Executing**
- Check spread conditions (must be ≤ 1.5 pips)
- Verify trading hours (9:00-18:00 GMT)
- Confirm all indicators are working
- Check daily trade limits

**Compilation Errors**
- Compile Supertrend.mq5 first
- Check MQL5 build compatibility
- Verify all files are in correct folders

**Poor Performance**
- Reduce risk amount
- Use VPS for execution
- Monitor during high volatility
- Check broker execution quality

### Performance Monitoring
- Monitor Expert tab for signals
- Check Journal for errors
- Review trade history weekly
- Analyze spread conditions

## 📊 Backtesting Guide

### Strategy Tester Settings
- **Model**: Every tick based on real ticks
- **Period**: M5 (recommended)
- **Spread**: Current or fixed at 1.5 pips
- **Execution Delay**: 100ms
- **Initial Deposit**: $50-$1000

### Optimization Parameters
- Risk Amount: $0.25 - $2.00
- Risk/Reward: 1.2 - 2.5
- Max Spread: 1.0 - 3.0 pips
- Supertrend Period: 8 - 15
- Supertrend Multiplier: 2.5 - 4.0

## 📝 Version History

### v1.00 (Current)
- ✅ Initial release
- ✅ Multi-indicator confirmation system
- ✅ Conservative risk management
- ✅ Session filtering
- ✅ Break-even functionality
- ✅ Comprehensive testing suite

### Planned Features (v1.1)
- 🔄 News filter integration
- 🔄 Multi-timeframe analysis
- 🔄 Advanced pattern recognition
- 🔄 Portfolio management

## ⚠️ Risk Disclaimer

**Important Notice**: This Expert Advisor is provided for educational and testing purposes. Forex trading involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results.

**Recommendations**:
- Always test on demo accounts first
- Never risk more than you can afford to lose
- Use proper risk management
- Monitor EA performance regularly
- Consider using a VPS for consistent execution

## 📞 Support

For questions, issues, or optimization requests:
1. Review the documentation thoroughly
2. Run the validation scripts
3. Check the troubleshooting section
4. Test on demo account first

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Happy Trading! 🚀**

*Remember: Consistent profitability comes from disciplined risk management, not from finding the "perfect" strategy.*
