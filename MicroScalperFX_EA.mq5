//+------------------------------------------------------------------+
//|                                                MicroScalperFX_EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Micro Scalper FX - High Frequency Scalping EA"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- Input parameters
input group "=== TRADING SETTINGS ==="
input double   InpRiskAmount = 0.50;           // Fixed risk amount per trade ($)
input double   InpMaxSpread = 1.5;             // Maximum spread (pips)
input int      InpMaxTradesPerDay = 5;         // Maximum trades per day per pair
input double   InpRiskRewardRatio = 1.5;       // Risk/Reward ratio
input bool     InpUseBreakEven = true;         // Use break-even after TP1

input group "=== INDICATOR SETTINGS ==="
input int      InpSupertrendPeriod = 10;       // Supertrend period
input double   InpSupertrendMultiplier = 3.0;  // Supertrend multiplier
input int      InpMacdFast = 12;               // MACD fast period
input int      InpMacdSlow = 26;               // MACD slow period
input int      InpMacdSignal = 9;              // MACD signal period
input int      InpEmaPeriod = 200;             // EMA period for trend filter
input int      InpAtrPeriod = 14;              // ATR period for SL/TP

input group "=== TIME SETTINGS ==="
input int      InpStartHour = 9;               // Trading start hour (GMT)
input int      InpEndHour = 18;                // Trading end hour (GMT)
input bool     InpAvoidNews = true;            // Avoid trading during news

input group "=== RISK MANAGEMENT ==="
input double   InpMaxDailyDrawdown = 3.0;      // Max daily drawdown (%)
input int      InpMaxConsecutiveLosses = 2;    // Max consecutive losses
input int      InpLockoutHours = 2;            // Lockout hours after max losses

//--- Global variables
CTrade         trade;
CPositionInfo  position;
CAccountInfo   account;

int            supertrendHandle;
int            macdHandle;
int            emaHandle;
int            atrHandle;

double         supertrendBuffer[];
double         supertrendColorBuffer[];
double         macdMainBuffer[];
double         macdSignalBuffer[];
double         emaBuffer[];
double         atrBuffer[];

datetime       lastTradeTime = 0;
int            dailyTradeCount = 0;
int            consecutiveLosses = 0;
datetime       lockoutUntil = 0;
double         dailyStartBalance = 0;
datetime       lastDayCheck = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialize indicators
    supertrendHandle = iCustom(_Symbol, PERIOD_CURRENT, "Supertrend", InpSupertrendPeriod, InpSupertrendMultiplier);
    macdHandle = iMACD(_Symbol, PERIOD_CURRENT, InpMacdFast, InpMacdSlow, InpMacdSignal, PRICE_CLOSE);
    emaHandle = iMA(_Symbol, PERIOD_CURRENT, InpEmaPeriod, 0, MODE_EMA, PRICE_CLOSE);
    atrHandle = iATR(_Symbol, PERIOD_CURRENT, InpAtrPeriod);
    
    if(supertrendHandle == INVALID_HANDLE || macdHandle == INVALID_HANDLE || 
       emaHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }
    
    //--- Set array as series
    ArraySetAsSeries(supertrendBuffer, true);
    ArraySetAsSeries(supertrendColorBuffer, true);
    ArraySetAsSeries(macdMainBuffer, true);
    ArraySetAsSeries(macdSignalBuffer, true);
    ArraySetAsSeries(emaBuffer, true);
    ArraySetAsSeries(atrBuffer, true);
    
    //--- Initialize daily balance
    dailyStartBalance = account.Balance();
    lastDayCheck = TimeCurrent();
    
    Print("MicroScalperFX EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handles
    IndicatorRelease(supertrendHandle);
    IndicatorRelease(macdHandle);
    IndicatorRelease(emaHandle);
    IndicatorRelease(atrHandle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check if new day
    CheckNewDay();
    
    //--- Check lockout
    if(TimeCurrent() < lockoutUntil)
        return;
    
    //--- Check daily limits
    if(!CheckDailyLimits())
        return;
    
    //--- Check trading time
    if(!IsTradingTime())
        return;
    
    //--- Check spread
    if(!CheckSpread())
        return;
    
    //--- Update indicator buffers
    if(!UpdateIndicators())
        return;
    
    //--- Check for existing position
    if(position.Select(_Symbol))
    {
        ManagePosition();
        return;
    }
    
    //--- Check for new signals
    CheckForSignals();
}

//+------------------------------------------------------------------+
//| Check if it's a new trading day                                 |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    datetime currentTime = TimeCurrent();
    MqlDateTime currentDT, lastDT;
    
    TimeToStruct(currentTime, currentDT);
    TimeToStruct(lastDayCheck, lastDT);
    
    if(currentDT.day != lastDT.day)
    {
        dailyTradeCount = 0;
        consecutiveLosses = 0;
        dailyStartBalance = account.Balance();
        lastDayCheck = currentTime;
        lockoutUntil = 0;
        
        Print("New trading day started. Reset counters.");
    }
}

//+------------------------------------------------------------------+
//| Check daily trading limits                                      |
//+------------------------------------------------------------------+
bool CheckDailyLimits()
{
    //--- Check max trades per day
    if(dailyTradeCount >= InpMaxTradesPerDay)
    {
        return false;
    }
    
    //--- Check daily drawdown
    double currentBalance = account.Balance();
    double drawdown = (dailyStartBalance - currentBalance) / dailyStartBalance * 100;
    
    if(drawdown >= InpMaxDailyDrawdown)
    {
        Print("Daily drawdown limit reached: ", drawdown, "%");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                   |
//+------------------------------------------------------------------+
bool IsTradingTime()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    return (dt.hour >= InpStartHour && dt.hour < InpEndHour);
}

//+------------------------------------------------------------------+
//| Check spread condition                                           |
//+------------------------------------------------------------------+
bool CheckSpread()
{
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    return (spread <= InpMaxSpread * 10); // Convert to points
}

//+------------------------------------------------------------------+
//| Update indicator buffers                                         |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
    if(CopyBuffer(supertrendHandle, 0, 0, 3, supertrendBuffer) <= 0 ||
       CopyBuffer(supertrendHandle, 1, 0, 3, supertrendColorBuffer) <= 0 ||
       CopyBuffer(macdHandle, 0, 0, 3, macdMainBuffer) <= 0 ||
       CopyBuffer(macdHandle, 1, 0, 3, macdSignalBuffer) <= 0 ||
       CopyBuffer(emaHandle, 0, 0, 3, emaBuffer) <= 0 ||
       CopyBuffer(atrHandle, 0, 0, 3, atrBuffer) <= 0)
    {
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckForSignals()
{
    //--- Prevent multiple trades on same bar
    if(lastTradeTime == iTime(_Symbol, PERIOD_CURRENT, 0))
        return;
    
    //--- Get current price data
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double close = iClose(_Symbol, PERIOD_CURRENT, 1);
    double high1 = iHigh(_Symbol, PERIOD_CURRENT, 1);
    double low1 = iLow(_Symbol, PERIOD_CURRENT, 1);
    double open1 = iOpen(_Symbol, PERIOD_CURRENT, 1);
    
    //--- Check for bullish signal
    if(CheckBullishSignal(close, high1, low1, open1))
    {
        OpenBuyTrade(ask);
    }
    //--- Check for bearish signal
    else if(CheckBearishSignal(close, high1, low1, open1))
    {
        OpenSellTrade(bid);
    }
}

//+------------------------------------------------------------------+
//| Check bullish signal conditions                                 |
//+------------------------------------------------------------------+
bool CheckBullishSignal(double close, double high, double low, double open)
{
    //--- Price above 200 EMA
    if(close <= emaBuffer[1])
        return false;

    //--- Supertrend bullish (green)
    if(supertrendColorBuffer[1] != 0) // 0 = green/bullish
        return false;

    //--- MACD histogram positive and rising
    if(macdMainBuffer[1] <= 0 || macdMainBuffer[1] <= macdMainBuffer[2])
        return false;

    //--- Check for bullish pin bar or engulfing pattern
    bool bullishPinBar = (close > open) && ((high - close) < (close - open) * 0.3) && ((open - low) > (close - open) * 2);
    bool bullishEngulfing = (close > open) && (open < iClose(_Symbol, PERIOD_CURRENT, 2)) && (close > iOpen(_Symbol, PERIOD_CURRENT, 2));

    return (bullishPinBar || bullishEngulfing);
}

//+------------------------------------------------------------------+
//| Check bearish signal conditions                                 |
//+------------------------------------------------------------------+
bool CheckBearishSignal(double close, double high, double low, double open)
{
    //--- Price below 200 EMA
    if(close >= emaBuffer[1])
        return false;

    //--- Supertrend bearish (red)
    if(supertrendColorBuffer[1] != 1) // 1 = red/bearish
        return false;

    //--- MACD histogram negative and falling
    if(macdMainBuffer[1] >= 0 || macdMainBuffer[1] >= macdMainBuffer[2])
        return false;

    //--- Check for bearish pin bar or engulfing pattern
    bool bearishPinBar = (close < open) && ((close - low) < (open - close) * 0.3) && ((high - open) > (open - close) * 2);
    bool bearishEngulfing = (close < open) && (open > iClose(_Symbol, PERIOD_CURRENT, 2)) && (close < iOpen(_Symbol, PERIOD_CURRENT, 2));

    return (bearishPinBar || bearishEngulfing);
}

//+------------------------------------------------------------------+
//| Open buy trade                                                  |
//+------------------------------------------------------------------+
void OpenBuyTrade(double price)
{
    double sl = price - (atrBuffer[1] * 0.7);
    double tp = price + (price - sl) * InpRiskRewardRatio;
    double lotSize = CalculateLotSize(price - sl);

    if(lotSize > 0)
    {
        if(trade.Buy(lotSize, _Symbol, price, sl, tp, "MicroScalperFX Buy"))
        {
            lastTradeTime = iTime(_Symbol, PERIOD_CURRENT, 0);
            dailyTradeCount++;
            Print("Buy trade opened: Price=", price, " SL=", sl, " TP=", tp, " Lot=", lotSize);
        }
        else
        {
            Print("Failed to open buy trade. Error: ", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| Open sell trade                                                 |
//+------------------------------------------------------------------+
void OpenSellTrade(double price)
{
    double sl = price + (atrBuffer[1] * 0.7);
    double tp = price - (sl - price) * InpRiskRewardRatio;
    double lotSize = CalculateLotSize(sl - price);

    if(lotSize > 0)
    {
        if(trade.Sell(lotSize, _Symbol, price, sl, tp, "MicroScalperFX Sell"))
        {
            lastTradeTime = iTime(_Symbol, PERIOD_CURRENT, 0);
            dailyTradeCount++;
            Print("Sell trade opened: Price=", price, " SL=", sl, " TP=", tp, " Lot=", lotSize);
        }
        else
        {
            Print("Failed to open sell trade. Error: ", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on fixed risk amount                   |
//+------------------------------------------------------------------+
double CalculateLotSize(double slDistance)
{
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    if(tickValue == 0 || tickSize == 0 || slDistance <= 0)
        return 0;

    double riskInQuoteCurrency = InpRiskAmount;
    double lotSize = (riskInQuoteCurrency * tickSize) / (slDistance * tickValue);

    //--- Normalize lot size
    lotSize = MathFloor(lotSize / lotStep) * lotStep;

    //--- Check limits
    if(lotSize < minLot) lotSize = minLot;
    if(lotSize > maxLot) lotSize = maxLot;

    return lotSize;
}

//+------------------------------------------------------------------+
//| Manage existing position                                         |
//+------------------------------------------------------------------+
void ManagePosition()
{
    if(!position.Select(_Symbol))
        return;

    double openPrice = position.PriceOpen();
    double currentPrice = (position.PositionType() == POSITION_TYPE_BUY) ?
                         SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    //--- Break-even logic
    if(InpUseBreakEven && position.StopLoss() != openPrice)
    {
        double sl = position.StopLoss();
        double tp = position.TakeProfit();
        double profit = 0;

        if(position.PositionType() == POSITION_TYPE_BUY)
        {
            profit = currentPrice - openPrice;
            double firstTP = openPrice + (openPrice - sl);

            if(currentPrice >= firstTP)
            {
                trade.PositionModify(_Symbol, openPrice + 10 * _Point, tp);
                Print("Break-even set for buy position");
            }
        }
        else
        {
            profit = openPrice - currentPrice;
            double firstTP = openPrice - (sl - openPrice);

            if(currentPrice <= firstTP)
            {
                trade.PositionModify(_Symbol, openPrice - 10 * _Point, tp);
                Print("Break-even set for sell position");
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Trade transaction event handler                                 |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    if(trans.symbol == _Symbol && trans.type == TRADE_TRANSACTION_DEAL_ADD)
    {
        if(trans.deal_type == DEAL_TYPE_BUY || trans.deal_type == DEAL_TYPE_SELL)
        {
            //--- Check if it's a closing deal
            if(trans.position != 0)
            {
                double profit = 0;
                if(HistoryDealSelect(trans.deal))
                {
                    profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);

                    if(profit < 0)
                    {
                        consecutiveLosses++;
                        Print("Loss registered. Consecutive losses: ", consecutiveLosses);

                        if(consecutiveLosses >= InpMaxConsecutiveLosses)
                        {
                            lockoutUntil = TimeCurrent() + InpLockoutHours * 3600;
                            Print("Max consecutive losses reached. Trading locked until: ", TimeToString(lockoutUntil));
                        }
                    }
                    else
                    {
                        consecutiveLosses = 0;
                        Print("Profit registered. Consecutive losses reset.");
                    }
                }
            }
        }
    }
}
