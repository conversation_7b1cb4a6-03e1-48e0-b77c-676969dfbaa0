# MicroScalperFX Expert Advisor Documentation

## Overview
MicroScalperFX is a high-frequency scalping Expert Advisor designed for small accounts ($50+) with ultra-conservative risk management and automated trading capabilities. The EA implements a multi-indicator confirmation system optimized for M1 and M5 timeframes on major forex pairs.

## Strategy Components

### Core Indicators
1. **Supertrend (10, 3.0)** - Primary trend direction filter
2. **Zero Lag MACD (12, 26, 9)** - Momentum confirmation
3. **200 EMA** - Higher timeframe trend filter
4. **ATR (14)** - Adaptive stop loss and take profit sizing

### Entry Conditions

#### Bullish Entry (Buy)
- Price above 200 EMA (trend filter)
- Supertrend showing green/bullish signal
- MACD histogram positive and rising
- Bullish pin bar or engulfing pattern on previous candle
- Spread ≤ 1.5 pips
- Within trading session (9:00-18:00 GMT)

#### Bearish Entry (Sell)
- Price below 200 EMA (trend filter)
- Supertrend showing red/bearish signal
- MACD histogram negative and falling
- Bearish pin bar or engulfing pattern on previous candle
- Spread ≤ 1.5 pips
- Within trading session (9:00-18:00 GMT)

## Risk Management Features

### Position Sizing
- **Fixed Risk**: $0.50 per trade (adjustable)
- **Dynamic Lot Calculation**: Based on ATR-derived stop loss
- **Minimum Lot**: Respects broker minimum lot size
- **Maximum Lot**: Respects broker maximum lot size

### Stop Loss & Take Profit
- **Stop Loss**: ATR(14) × 0.7 (typically 3-5 pips)
- **Take Profit**: Stop Loss × Risk/Reward Ratio (default 1.5)
- **Break-Even**: Automatically moves SL to break-even after first TP level

### Daily Limits
- **Maximum Trades**: 5 per day per symbol
- **Daily Drawdown**: 3% maximum
- **Consecutive Losses**: 2 maximum before 2-hour lockout
- **Session Filter**: London/NY sessions only

## Installation Instructions

### Step 1: Copy Files
1. Copy `MicroScalperFX_EA.mq5` to `MQL5/Experts/` folder
2. Copy `Supertrend.mq5` to `MQL5/Indicators/` folder
3. Copy `Test_MicroScalperFX.mq5` to `MQL5/Scripts/` folder

### Step 2: Compile Indicators
1. Open MetaEditor
2. Compile `Supertrend.mq5` first
3. Compile `MicroScalperFX_EA.mq5`
4. Compile `Test_MicroScalperFX.mq5`

### Step 3: Run Tests
1. Attach `Test_MicroScalperFX` script to chart
2. Verify all indicators load successfully
3. Check symbol information and trading permissions

## Configuration Parameters

### Trading Settings
- **InpRiskAmount**: Fixed risk per trade in account currency ($0.50)
- **InpMaxSpread**: Maximum allowed spread in pips (1.5)
- **InpMaxTradesPerDay**: Daily trade limit per symbol (5)
- **InpRiskRewardRatio**: Risk/reward ratio for take profit (1.5)
- **InpUseBreakEven**: Enable break-even functionality (true)

### Indicator Settings
- **InpSupertrendPeriod**: Supertrend ATR period (10)
- **InpSupertrendMultiplier**: Supertrend multiplier (3.0)
- **InpMacdFast**: MACD fast EMA period (12)
- **InpMacdSlow**: MACD slow EMA period (26)
- **InpMacdSignal**: MACD signal line period (9)
- **InpEmaPeriod**: Trend filter EMA period (200)
- **InpAtrPeriod**: ATR period for SL/TP calculation (14)

### Time Settings
- **InpStartHour**: Trading start hour GMT (9)
- **InpEndHour**: Trading end hour GMT (18)
- **InpAvoidNews**: Avoid trading during news events (true)

### Risk Management
- **InpMaxDailyDrawdown**: Maximum daily drawdown percentage (3.0)
- **InpMaxConsecutiveLosses**: Maximum consecutive losses (2)
- **InpLockoutHours**: Lockout period after max losses (2)

## Recommended Settings

### For $50 Account
- Risk Amount: $0.50 (1% of account)
- Max Spread: 1.5 pips
- Max Trades/Day: 3-5
- Risk/Reward: 1.5
- Timeframe: M5
- Pairs: EURUSD, USDJPY, GBPUSD

### For $100 Account
- Risk Amount: $1.00 (1% of account)
- Max Spread: 2.0 pips
- Max Trades/Day: 5-7
- Risk/Reward: 1.5-2.0
- Timeframe: M1 or M5
- Pairs: Major pairs + AUDUSD, USDCAD

## Broker Requirements

### Essential Features
- **ECN/STP Execution**: Low spreads and fast execution
- **Minimum Spread**: ≤ 1.5 pips on major pairs
- **Minimum Lot**: 0.01 or lower
- **Leverage**: 1:100 or higher
- **VPS Recommended**: For consistent execution

### Recommended Brokers
- IC Markets
- Pepperstone
- FTMO (for prop trading)
- MyForexFunds (for prop trading)

## Performance Expectations

### Backtesting Results (Estimated)
- **Win Rate**: 60-75%
- **Average RR**: 1.5
- **Trades/Week**: 15-30
- **Monthly Return**: 10-30%
- **Maximum Drawdown**: ≤10%

### Live Trading Considerations
- **Slippage**: 0.5-1 pip expected
- **Spread Variation**: Monitor during news
- **VPS Latency**: <50ms recommended
- **Internet Stability**: Critical for scalping

## Troubleshooting

### Common Issues
1. **No Trades Executing**
   - Check spread conditions
   - Verify trading hours
   - Confirm indicator signals
   - Check daily limits

2. **Compilation Errors**
   - Ensure Supertrend.mq5 is compiled first
   - Check MQL5 syntax compatibility
   - Verify all includes are available

3. **Poor Performance**
   - Reduce risk amount
   - Tighten spread filter
   - Use VPS for execution
   - Monitor during high volatility

### Log Analysis
- Monitor Expert tab for trade signals
- Check Journal for error messages
- Review trade history for patterns
- Analyze spread conditions during trades

## Support and Updates

### Version History
- v1.00: Initial release with core functionality

### Future Enhancements
- News filter integration
- Multi-timeframe analysis
- Advanced pattern recognition
- Portfolio management features

### Contact Information
- Documentation: This file
- Testing: Use Test_MicroScalperFX.mq5 script
- Validation: Run Strategy Tester with real tick data

## Disclaimer
This EA is designed for educational and testing purposes. Past performance does not guarantee future results. Always test thoroughly on demo accounts before live trading. Use appropriate risk management and never risk more than you can afford to lose.
